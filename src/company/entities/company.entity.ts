import {BaseEntity} from "../../database/entities/base.entity";
import {Column, Entity, ManyToMany, OneToMany} from "typeorm";
import {ApiProperty} from "@nestjs/swagger";
import {Client} from "../../client/entities/client.entity";
import {Tenant} from "../../tenant/entities/tenant.entity";
import {Valuation} from "../../valuations/entities/valuation.entity";

@Entity('company')
export class Company extends BaseEntity {
    @Column({unique: true})
    @ApiProperty({description: 'The unique name of the company'})
    name: string;

    @Column()
    @ApiProperty({description: 'The unique company contact person'})
    contactPerson: string;

    @Column({unique: true})
    @ApiProperty({description: 'The unique email of the company'})
    email: string;

    @Column({unique: true})
    @ApiProperty({description: 'The unique company phone number'})
    phone: string;

    @ManyToMany(() => Client, client => client.companies)
    @ApiProperty({description: 'Clients associated with this company', type: () => [Client]})
    clients: Client[];

    @ManyToMany(() => Tenant, tenant => tenant.companies)
    @ApiProperty({description: 'Tenants associated with this company', type: () => [Tenant]})
    tenants: Tenant[];

    @OneToMany(() => Valuation, (valuation) => valuation.company)
    @ApiProperty({description: 'Valuations associated with this company', type: () => [Valuation]})
    valuations: Valuation[];
}

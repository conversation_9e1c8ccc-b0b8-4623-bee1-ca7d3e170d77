const apiBaseUrl = import.meta.env.VITE_BACKEND_API_BASE_URL || 'http://localhost:3000/api/v1';

export default {
    // Auth endpoints
    login: () => `${apiBaseUrl}/api/v1/auth/login`,
    logout: () => `${apiBaseUrl}/api/v1/auth/logout`,
    refreshToken: () => `${apiBaseUrl}/api/v1/auth/refresh-token`,

    // User endpoints
    allUsers: () => `${apiBaseUrl}/api/v1/users`,
    user: (id: string) => `${apiBaseUrl}/api/v1/users/${id}`,
    users: ({page, pageSize}: { page: number; pageSize: number }) =>
        `${apiBaseUrl}/api/v1/users/?page=${page}&pageSize=${pageSize}`,

    // Role endpoints
    allRoles: () => `${apiBaseUrl}/api/v1/roles`,
    role: (id: string) => `${apiBaseUrl}/api/v1/roles/${id}`,
    roles: ({page, pageSize}: { page: number; pageSize: number }) =>
        `${apiBaseUrl}/api/v1/roles/?page=${page}&pageSize=${pageSize}`,

    // Permission endpoints
    allPermissions: () => `${apiBaseUrl}/api/v1/permissions`,
    permission: (id: string) => `${apiBaseUrl}/api/v1/permissions/${id}`,

    // Project endpoints
    allProjects: () => `${apiBaseUrl}/projects`,
    project: (id: string) => `${apiBaseUrl}/projects/${id}`,
    projects: ({page, pageSize}: { page: number; pageSize: number }) =>
        `${apiBaseUrl}/projects/?page=${page}&pageSize=${pageSize}`,

    // Tenants endpoints
    allTenants: (options?: any) => {
        if (!options) return `${apiBaseUrl}/api/v1/tenant`
        const params = new URLSearchParams()
        if (options.page) params.append('page', options.page.toString())
        if (options.perPage) params.append('perPage', options.perPage.toString())
        if (options.sortBy) params.append('sort', options.sortBy)
        if (options.sortingOrder) params.append('order', options.sortingOrder)
        return `${apiBaseUrl}/api/v1/tenant?${params.toString()}`
    },
    tenant: (id: string) => `${apiBaseUrl}/api/v1/tenant/${id}`,
    tenants: ({page, pageSize}: { page: number; pageSize: number }) =>
        `${apiBaseUrl}/api/v1/tenant/?page=${page}&pageSize=${pageSize}`,

    // Vehicle types endpoints
    allVehicleTypes: () => `${apiBaseUrl}/api/v1/vehicle-type`,
    vehicleType: (id: string) => `${apiBaseUrl}/api/v1/vehicle-type/${id}`,
    vehicleTypes: ({page, pageSize}: { page: number; pageSize: number }) =>
        `${apiBaseUrl}/api/v1/vehicle-type/?page=${page}&pageSize=${pageSize}`,

    // Other endpoints
    avatars: () => `${apiBaseUrl}/avatars`,
};

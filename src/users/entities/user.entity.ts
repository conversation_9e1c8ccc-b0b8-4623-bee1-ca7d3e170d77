import {<PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Role } from './role.entity';
import { BaseEntity } from '../../database/entities/base.entity';
import {Valuation} from "../../valuations/entities/valuation.entity";

@Entity('users')
export class User extends BaseEntity {
    @Column({ unique: true })
    @ApiProperty({ description: 'The unique username of the user' })
    username: string;

    @Column({ unique: true })
    @ApiProperty({ description: 'The email address of the user' })
    email: string;

    @Column()
    @ApiProperty({ description: 'The hashed password of the user' })
    password: string;

    @ApiProperty({ description: 'The first name of the user' })
    @Column({ nullable: true })
    firstName: string;

    @ApiProperty({ description: 'The last name of the user' })
    @Column({ nullable: true })
    lastName: string;

    @ApiProperty({ description: 'The phone number of the user' })
    @Column()
    phone: string;

    @Column({ default: true })
    @ApiProperty({ description: 'Whether the user is active' })
    isActive: boolean;

    @ManyToMany(() => Role, role => role.users)
    @JoinTable({
        name: 'user_roles',
        joinColumn: { name: 'user_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' }
    })
    @ApiProperty({ description: 'The roles assigned to the user' })
    roles: Role[];

    @OneToMany(() => Valuation, (valuation) => valuation.createdBy )
    createdValuations: Valuation[];

    @OneToMany(() => Valuation, (valuation) => valuation.scheduledBy )
    scheduledValuations: Valuation[];

    @OneToMany(() => Valuation, (valuation) => valuation.valuedBy )
    valuedValuations: Valuation[];

    @OneToMany(() => Valuation, (valuation) => valuation.approvedBy )
    approvedValuations: Valuation[];

    @OneToMany(() => Valuation, (valuation) => valuation.modifiedBy )
    modifiedValuations: Valuation[];
}

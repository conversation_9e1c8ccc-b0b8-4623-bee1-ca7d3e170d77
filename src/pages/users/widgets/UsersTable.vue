<script setup lang="ts">
import { defineVaDataTableColumns, useModal } from 'vuestic-ui';
import { User, Role } from '../types';
import UserAvatar from './UserAvatar.vue';
import { computed, PropType, ref, toRef } from 'vue';
import { Pagination, Sorting } from '../../../data/pages/users';
import { useVModel } from '@vueuse/core';

const columns = defineVaDataTableColumns([
    { label: 'First Name', key: 'firstName', sortable: true },
    { label: 'Last Name', key: 'lastName', sortable: true },
    { label: 'Email', key: 'email', sortable: true },
    { label: 'Username', key: 'username', sortable: true },
    { label: 'Phone', key: 'phone', sortable: false },
    { label: 'Role (s)', key: 'role', sortable: true },
    { label: 'Updated At', key: 'updatedAt', sortable: true },
    { label: 'Created At', key: 'createdAt', sortable: true },
    { label: 'Actions', key: 'actions', align: 'right' },
]);

const props = defineProps({
    users: {
        type: Array as PropType<User[]>,
        required: true,
    },
    roles: {
        type: Array as PropType<Role[]>,
        required: true,
    },
    loading: { type: Boolean, default: false },
    pagination: { type: Object as PropType<Pagination>, required: true },
    sortBy: { type: String as PropType<Sorting['sortBy']>, required: true },
    sortingOrder: { type: String as PropType<Sorting['sortingOrder']>, default: null },
});

const emit = defineEmits<{
    (event: 'edit-user', user: User): void
    (event: 'delete-user', user: User): void
    (event: 'update:sortBy', sortBy: Sorting['sortBy']): void
    (event: 'update:sortingOrder', sortingOrder: Sorting['sortingOrder']): void
}>();

const users = toRef(props, 'users');
const sortByVModel = useVModel(props, 'sortBy', emit);
const sortingOrderVModel = useVModel(props, 'sortingOrder', emit);

const totalPages = computed(() => Math.ceil(props.pagination.total / props.pagination.perPage));

const { confirm } = useModal();

const onUserDelete = async (user: User) => {
    const agreed = await confirm({
        title: 'Delete user',
        message: `Are you sure you want to delete ${user.firstName} ${user.lastName}?`,
        okText: 'Delete',
        cancelText: 'Cancel',
        size: 'small',
        maxWidth: '380px',
    });

    if (agreed) {
        emit('delete-user', user);
    }
};


// Store dynamically assigned role colors
const roleColors = ref<Record<string, string>>({});

// Function to generate a random color
const getRandomColor = () => {
    const colors = ['red', 'blue', 'green', 'purple', 'orange', 'teal', 'pink', 'cyan'];
    return colors[Math.floor(Math.random() * colors.length)];
};
// Function to get or assign a color for a role
const getRoleColor = (roleName: string) => {
    if (!roleColors.value[roleName]) {
        roleColors.value[roleName] = getRandomColor();
    }
    return roleColors.value[roleName];
};

const formatRoleNames = (roles: Role[]) => {
    if (!roles || roles.length === 0) {
        return [{ name: 'No allocated roles' }];
    }

    return roles.sort((a, b) => a.name.localeCompare(b.name));
};

const formatUserRoleNames = (roles: Role[]) => {
    if (!roles || roles.length === 0) return 'No roles';

    const names = roles.map(role => role.name);
    if (names.length <= 2) {
        return names.join(', ');
    }

    return (
        names
            .slice(0, 2)
            .join(', ') +
        ' + ' +
        (names.length - 2) +
        ' more'
    );
};



</script>

<template>
    <VaDataTable
        v-model:sort-by="sortByVModel"
        v-model:sorting-order="sortingOrderVModel"
        :columns="columns"
        :items="users"
        :loading="$props.loading"
    >
        <template #cell(firstName)="{ rowData }">
            <div class="flex items-center gap-2 max-w-[230px] ellipsis">
                <UserAvatar :user="rowData as User" size="small" />
                {{ rowData.firstName }}
            </div>
        </template>

        <template #cell(username)="{ rowData }">
            <div class="max-w-[120px] ellipsis">
                {{ rowData.username }}
            </div>
        </template>

        <template #cell(email)="{ rowData }">
            <div class="ellipsis max-w-[230px]">
                {{ rowData.email }}
            </div>
        </template>

        <template #cell(role)="{ rowData }">
            <div class="flex flex-wrap gap-1">
                <template v-if="rowData.roles && rowData.roles.length">
                    <VaBadge
                        v-for="role in formatRoleNames(rowData.roles)"
                        :key="role.id"
                        :text="role.name"
                        :color="getRoleColor(role.name)"
                        class="mb-1"
                    />
                </template>
                <VaBadge v-else text="No allocated roles" color="gray" />
            </div>
        </template>


        <template #cell(updatedAt)="{ rowData }">
            <div class="max-w-[150px] ellipsis">
                {{ new Date(rowData.updatedAt).toISOString().slice(0, 19).replace('T', ' ') }}
            </div>
        </template>
        <template #cell(createdAt)="{ rowData }">
            <div class="max-w-[150px] ellipsis">
                {{ new Date(rowData.createdAt).toISOString().slice(0, 19).replace('T', ' ') }}

            </div>
        </template>
        <template #cell(actions)="{ rowData }">
            <div class="flex gap-2 justify-end">
                <VaButton
                    preset="primary"
                    size="small"
                    icon="mso-edit"
                    aria-label="Edit user"
                    @click="$emit('edit-user', rowData as User)"
                />
                <VaButton
                    preset="primary"
                    size="small"
                    icon="mso-delete"
                    color="danger"
                    aria-label="Delete user"
                    @click="onUserDelete(rowData as User)"
                />
            </div>
        </template>
    </VaDataTable>

    <div class="flex flex-col-reverse md:flex-row gap-2 justify-between items-center py-2">
        <div>
            <b>{{ $props.pagination.total }} results.</b>
            Results per page
            <VaSelect v-model="$props.pagination.perPage" class="!w-20" :options="[10, 50, 100]" />
        </div>

        <div v-if="totalPages > 1" class="flex">
            <VaButton
                preset="secondary"
                icon="va-arrow-left"
                aria-label="Previous page"
                :disabled="$props.pagination.page === 1"
                @click="$props.pagination.page--"
            />
            <VaButton
                class="mr-2"
                preset="secondary"
                icon="va-arrow-right"
                aria-label="Next page"
                :disabled="$props.pagination.page === totalPages"
                @click="$props.pagination.page++"
            />
            <VaPagination
                v-model="$props.pagination.page"
                buttons-preset="secondary"
                :pages="totalPages"
                :visible-pages="5"
                :boundary-links="false"
                :direction-links="false"
            />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.va-data-table {
    ::v-deep(.va-data-table__table-tr) {
        border-bottom: 1px solid var(--va-background-border);
    }
}
</style>

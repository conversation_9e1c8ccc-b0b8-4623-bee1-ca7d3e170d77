<script setup lang="ts">
import { defineVaDataTableColumns, useModal } from 'vuestic-ui';
import { Role, Permission } from '../types';
import { computed, PropType, ref, toRef } from 'vue';
import { Pagination, Sorting } from '../../../data/pages/roles';
import { useVModel } from '@vueuse/core';

const columns = defineVaDataTableColumns([
    { label: 'Name', key: 'name', sortable: true },
    { label: 'Description', key: 'description', sortable: true },
    { label: 'Permissions', key: 'permissions', sortable: true },
    { label: 'Updated At', key: 'updatedAt', sortable: true },
    { label: 'Created At', key: 'createdAt', sortable: true },
    { label: 'Actions', key: 'actions', align: 'right' },
]);

const props = defineProps({
    roles: {
        type: Array as PropType<Role[]>,
        required: true,
    },
    loading: { type: Boolean, default: false },
    pagination: { type: Object as PropType<Pagination>, required: true },
    sortBy: { type: String as PropType<Sorting['sortBy']>, required: true },
    sortingOrder: { type: String as PropType<Sorting['sortingOrder']>, default: null },
});

const emit = defineEmits<{
    (event: 'edit-role', role: Role): void
    (event: 'delete-role', role: Role): void
    (event: 'update:sortBy', sortBy: Sorting['sortBy']): void
    (event: 'update:sortingOrder', sortingOrder: Sorting['sortingOrder']): void
}>();

const roles = toRef(props, 'roles');
const sortByVModel = useVModel(props, 'sortBy', emit);
const sortingOrderVModel = useVModel(props, 'sortingOrder', emit);

const totalPages = computed(() => Math.ceil(props.pagination.total / props.pagination.perPage));

const { confirm } = useModal();

const onRoleDelete = async (role: Role) => {
    const agreed = await confirm({
        title: 'Delete role',
        message: `Are you sure you want to delete ${role.name}?`,
        okText: 'Delete',
        cancelText: 'Cancel',
        size: 'small',
        maxWidth: '380px',
    });

    if (agreed) {
        emit('delete-role', role);
    }
};


// Store dynamically assigned permission colors
const permissionColors = ref<Record<string, string>>({});

// Function to generate and store a unique color for each role
const getRoleColor = (permissionName: string) => {
    if (!permissionColors.value[permissionName]) {
        const colors = ['red', 'blue', 'green', 'purple', 'orange', 'teal', 'pink', 'cyan'];
        permissionColors.value[permissionName] = colors[Math.floor(Math.random() * colors.length)];
    }
    return permissionColors.value[permissionName];
};

// Function to extract and sort permission names
const sortedPermissions = (permissions: Permission[]) => {
    if (!permissions || permissions.length === 0) {
        return [{ name: 'No allocated permissions' }];
    }

    return permissions.sort((a, b) => a.name.localeCompare(b.name));
};
</script>

<template>
    <VaDataTable v-model:sort-by="sortByVModel" v-model:sorting-order="sortingOrderVModel" :columns="columns"
                 :items="roles" :loading="$props.loading">
        <template #cell(name)="{ rowData }">
            <div class="flex items-center gap-2 max-w-[230px] ellipsis">
                {{ rowData.name }}
            </div>
        </template>

        <template #cell(description)="{ rowData }">
            <div class="max-w-[200px] ellipsis">
                {{ rowData.description }}
            </div>
        </template>

        <template #cell(permissions)="{ rowData }">
            <div class="flex flex-wrap gap-1">
                <template v-if="rowData.permissions && rowData.permissions.length">
                    <VaBadge
                        v-for="permission in sortedPermissions(rowData.permissions)"
                        :key="permission.id"
                        :text="permission.name"
                        :color="getRoleColor(permission.name)"
                        class="mb-1"
                    />
                </template>
                <VaBadge v-else text="No allocated permissions" color="gray" />
            </div>
        </template>

        <template #cell(updatedAt)="{ rowData }">
            <div class="max-w-[150px] ellipsis">
                {{ new Date(rowData.updatedAt).toISOString().slice(0, 19).replace('T', ' ') }}
            </div>
        </template>
        <template #cell(createdAt)="{ rowData }">
            <div class="max-w-[150px] ellipsis">
                {{ new Date(rowData.createdAt).toISOString().slice(0, 19).replace('T', ' ') }}

            </div>
        </template>

        <template #cell(actions)="{ rowData }">
            <div class="flex gap-2 justify-end">
                <VaButton preset="primary" size="small" icon="mso-edit" aria-label="Edit role"
                          @click="$emit('edit-role', rowData as Role)" />
                <VaButton preset="primary" size="small" icon="mso-delete" color="danger" aria-label="Delete role"
                          @click="onRoleDelete(rowData as Role)" />
            </div>
        </template>
    </VaDataTable>

    <div class="flex flex-col-reverse md:flex-row gap-2 justify-between items-center py-2">
        <div>
            <b>{{ $props.pagination.total }} results.</b>
            Results per page
            <VaSelect v-model="$props.pagination.perPage" class="!w-20" :options="[10, 50, 100]" />
        </div>

        <div v-if="totalPages > 1" class="flex">
            <VaButton preset="secondary" icon="va-arrow-left" aria-label="Previous page"
                      :disabled="$props.pagination.page === 1" @click="$props.pagination.page--" />
            <VaButton class="mr-2" preset="secondary" icon="va-arrow-right" aria-label="Next page"
                      :disabled="$props.pagination.page === totalPages" @click="$props.pagination.page++" />
            <VaPagination v-model="$props.pagination.page" buttons-preset="secondary" :pages="totalPages"
                          :visible-pages="5" :boundary-links="false" :direction-links="false" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.va-data-table {
    ::v-deep(.va-data-table__table-tr) {
        border-bottom: 1px solid var(--va-background-border);
    }
}
</style>

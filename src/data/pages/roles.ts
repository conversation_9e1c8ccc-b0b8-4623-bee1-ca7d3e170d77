import {Role } from '../../pages/roles/types';
import api from '../../services/api';
import axios from 'axios';
import { authService } from '../../services/auth.service';

export type Pagination = {
    page: number
    perPage: number
    total: number
}

export type Sorting = {
    sortBy: keyof Role | undefined
    sortingOrder: 'asc' | 'desc' | null
}

export type Filters = {
    search: string
}

export const getRoles = async (filters: Partial<Filters & Pagination & Sorting>) => {
    const { search } = filters;

    let response = await axios.get(api.allRoles(), {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + authService.getAuthToken(),
        },
    });

    let filteredRoles:Role[] = response.data;

    if (search) {
        filteredRoles = filteredRoles.filter((role) => role.name.toLowerCase().includes(search.toLowerCase()));
    }

    const { page = 1, perPage = 10 } = filters || {};
    return {
        data: filteredRoles,
        pagination: {
            page,
            perPage,
            total: filteredRoles.length,
        },
    };
};

export const addRole = async (role: Role) => {
    const response = await axios.post(api.allRoles(), role, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + authService.getAuthToken(),
        },
    });

    return [response.data];
};

export const updateRole = async (role: Role) => {
    const response = await axios.patch(api.role(role.id), role, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + authService.getAuthToken(),
        },
    });

    return [response.data];
};

export const removeRole = async (role: Role) => {
    const response = await axios.delete(api.role(role.id), {
        headers: {
            'Authorization': 'Bearer ' + authService.getAuthToken(),
        },
    });

    return response.status === 200;
};

export const uploadAvatar = async (body: FormData) => {
    return fetch(api.avatars(), { method: 'POST', body, redirect: 'follow' }).then((r) => r.json());
};

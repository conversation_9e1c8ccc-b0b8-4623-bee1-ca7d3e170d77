import { User } from '../../pages/users/types';
import api from '../../services/api';
import axios from 'axios';
import { authService } from '../../services/auth.service';

export type Pagination = {
    page: number
    perPage: number
    total: number
}

export type Sorting = {
    sortBy: keyof User | undefined
    sortingOrder: 'asc' | 'desc' | null
}

export type Filters = {
    isActive: boolean
    search: string
}

export const getUsers = async (filters: Partial<Filters & Pagination & Sorting>) => {
    const { isActive, search } = filters;

    let response = await axios.get(api.allUsers(), {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + authService.getAuthToken(),
        },
    });

    let filteredUsers: User[] = response.data;

    filteredUsers = filteredUsers.filter((user) => user.isActive === isActive);

    if (search) {
        filteredUsers = filteredUsers.filter((user) => user.firstName.toLowerCase().includes(search.toLowerCase()));
    }

    const { page = 1, perPage = 10 } = filters || {};
    return {
        data: filteredUsers,
        pagination: {
            page,
            perPage,
            total: filteredUsers.length,
        },
    };
};

export const addUser = async (user: User & { password?: string }) => {
    // Convert user to DTO format expected by backend
    const userDto = {
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        isActive: user.isActive,
        password: user.password || 'defaultPassword123',
        roleIds: user.roles?.map(r => r.id) || []
    };

    const response = await axios.post(api.allUsers(), userDto, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + authService.getAuthToken(),
        },
    });

    return [response.data];
};

export const updateUser = async (user: User) => {
    // Convert user to DTO format expected by backend
    const userDto = {
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        isActive: user.isActive,
        roleIds: user.roles?.map(r => r.id) || []
    };

    const response = await axios.patch(api.user(user.id), userDto, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + authService.getAuthToken(),
        },
    });

    return [response.data];
};

export const removeUser = async (user: User) => {
    const response = await axios.delete(api.user(user.id), {
        headers: {
            'Authorization': 'Bearer ' + authService.getAuthToken(),
        },
    });

    return response.status === 200;
};

export const uploadAvatar = async (body: FormData) => {
    return fetch(api.avatars(), { method: 'POST', body, redirect: 'follow' }).then((r) => r.json());
};
